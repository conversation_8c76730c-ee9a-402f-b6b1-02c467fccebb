import { DashboardIcon, FileTextIcon, IntegrationsIcon, SettingsIcon, UsersIcon, WandIcon } from './components/Icons';
import AnalysisHelpScreenshot from './components/help-screenshots/AnalysisHelpScreenshot';
import ApiSsoHelpScreenshot from './components/help-screenshots/ApiSsoHelpScreenshot';
import ClauseInsertionScreenshot from './components/help-screenshots/ClauseInsertionScreenshot';
import ClauseLibraryHelpScreenshot from './components/help-screenshots/ClauseLibraryHelpScreenshot';
import ClientsHelpScreenshot from './components/help-screenshots/ClientsHelpScreenshot';
import CommentingScreenshot from './components/help-screenshots/CommentingScreenshot';
import DashboardHelpScreenshot from './components/help-screenshots/DashboardHelpScreenshot';
import FoldersHelpScreenshot from './components/help-screenshots/FoldersHelpScreenshot';
import GenerateHelpScreenshot from './components/help-screenshots/GenerateHelpScreenshot';
import IntegrationsHelpScreenshot from './components/help-screenshots/IntegrationsHelpScreenshot';
import LifecycleHelpScreenshot from './components/help-screenshots/LifecycleHelpScreenshot';
import ObligationsHelpScreenshot from './components/help-screenshots/ObligationsHelpScreenshot';
import SearchHelpScreenshot from './components/help-screenshots/SearchHelpScreenshot';
import SettingsHelpScreenshot from './components/help-screenshots/SettingsHelpScreenshot';
import SharingScreenshot from './components/help-screenshots/SharingScreenshot';
import SignaturesHelpScreenshot from './components/help-screenshots/SignaturesHelpScreenshot';
import TeamManagementHelpScreenshot from './components/help-screenshots/TeamManagementHelpScreenshot';
import VersioningHelpScreenshot from './components/help-screenshots/VersioningHelpScreenshot';
import WorkflowsHelpScreenshot from './components/help-screenshots/WorkflowsHelpScreenshot';
import { ChatMessage, HelpCategory, MessageRole, Template, WorkflowTemplate } from './types';

export const ANONYMOUS_USER_QUOTA = 3;

export const SYSTEM_PROMPT = `You are Lexi, an expert AI legal assistant who engages in thoughtful, multi-turn conversations to create the best possible legal documents for users.

CONVERSATIONAL APPROACH:
When users request contracts or legal documents, engage in a collaborative conversation:

1. INITIAL RESPONSE: Acknowledge their request warmly and ask 2-3 specific clarifying questions about:
   - Parties involved (names, types: individual/company)
   - Jurisdiction (state/country where enforceable)
   - Key terms or special requirements
   - Timeline or duration considerations

2. REQUIREMENT GATHERING: Based on their answers, ask follow-up questions to gather complete details. Be specific and helpful.

3. CONFIRMATION: Summarize all collected information and ask for confirmation before proceeding.

4. DOCUMENT GENERATION: Only after gathering sufficient details, create the legal document.

DOCUMENT FORMAT:
When generating the final contract, use this format:
- Start with: ---CONTRACT START---
- Include well-structured legal content with proper headings and clauses
- End with: ---CONTRACT END---

CONVERSATION STYLE:
- Be warm, professional, and conversational
- Ask one question at a time when possible
- Provide helpful suggestions and options
- Always include appropriate legal disclaimers
- Explain legal concepts when helpful

For simple questions or general legal advice, respond conversationally without the document markers.`;

export const INITIAL_MESSAGE: ChatMessage = {
    id: 'initial-message',
    role: MessageRole.MODEL,
    content: "Hello! I'm Lexi, your AI legal assistant. I'm here to help you create professional legal documents through a collaborative conversation.\n\nI can help you draft contracts like NDAs, employment agreements, freelance contracts, lease agreements, and more. Just tell me what kind of document you need, and I'll ask a few questions to make sure it's perfectly tailored to your situation.\n\nWhat legal document can I help you create today?"
};

export const HOW_IT_WORKS_STEPS = [
  {
    title: 'Draft with AI',
    description: 'Describe your needs in plain English. Our AI assistant will generate a professional legal document in seconds, tailored to your requirements.',
    icon: 'SparklesIcon'
  },
  {
    title: 'Review & Refine',
    description: 'Easily edit the generated document, add custom clauses from your library, and collaborate with your team in real-time with comments.',
    icon: 'EditIcon'
  },
  {
    title: 'Manage & Sign',
    description: 'Track document statuses on a visual pipeline, manage versions, and send for legally-binding e-signatures, all within one platform.',
    icon: 'SignatureIcon'
  }
];

export const FAQ_DATA = [
  {
    question: 'Is LexiGen a law firm?',
    answer: 'No, LexiGen is not a law firm and does not provide legal advice. The AI-generated content is for informational purposes only and should be considered a starting point. We always recommend consulting with a qualified attorney for your specific legal needs.'
  },
  {
    question: 'How accurate are the AI-generated documents?',
    answer: 'Our AI is trained on a vast corpus of legal documents and aims for high accuracy and relevance. However, it is not infallible. All generated documents should be carefully reviewed for accuracy and suitability for your specific jurisdiction and situation.'
  },
  {
    question: 'Can I use the documents for commercial purposes?',
    answer: 'Yes, once you generate a document, you have the rights to use and modify it for your personal or commercial needs. However, you are solely responsible for the legal validity of the final document.'
  },
  {
    question: 'Is my data secure?',
    answer: 'Yes, we take data security very seriously. We use industry-standard encryption for data in transit and at rest. Please review our Privacy Policy for more detailed information on how we handle your data.'
  },
  {
    question: 'Can I cancel my subscription at any time?',
    answer: 'Absolutely. You can manage your subscription from your account settings. If you cancel, you will retain access to your plan\'s features until the end of the current billing period.'
  }
];

export const HELP_TOPICS: HelpCategory[] = [
  {
    category: 'Getting Started',
    icon: DashboardIcon,
    topics: [
      { id: 'welcome', title: 'Welcome to LexiGen', content: `<h2>Welcome to LexiGen!</h2><p>This guide will help you get started with the platform's key features. Use the navigation on the left to explore different topics.</p><p>LexiGen is an all-in-one platform for drafting, managing, and analyzing legal documents using the power of AI. Here's a quick overview of what you can do:</p><ul><li><strong>Generate Documents:</strong> Use our AI assistant to draft contracts from scratch.</li><li><strong>Collaborate:</strong> Work with clients and team members in real-time.</li><li><strong>Automate:</strong> Set up workflows to automate approvals and other repetitive tasks.</li><li><strong>Analyze:</strong> Get AI-powered insights on third-party documents to identify risks.</li></ul>` },
      {
        id: 'dashboard',
        title: 'Navigating the Dashboard',
        content: [
          { type: 'text', value: `<h2>Navigating the Dashboard</h2><p>The main dashboard is your command center. It provides a quick summary of your account activity.</p>` },
          { type: 'screenshot', component: DashboardHelpScreenshot },
          { type: 'text', value: `<ol><li><strong>Stat Cards:</strong> At the top, you'll find key metrics like the total number of documents, folders created, and your monthly generation quota.</li><li><strong>Recent Activity Chart:</strong> This chart visualizes the number of documents you've created over the past 7 days.</li><li><strong>Sidebar Navigation:</strong> Use the sidebar on the left to access all the main features of the application.</li></ol>` }
        ]
      },
    ]
  },
  {
    category: 'Core Features',
    icon: FileTextIcon,
    topics: [
       {
        id: 'generate',
        title: 'Generating Documents with AI',
        content: [
            { type: 'text', value: `<h2>Generating a New Document</h2><p>You can create a new legal document by describing your needs to the AI assistant.</p>` },
            { type: 'screenshot', component: GenerateHelpScreenshot },
            { type: 'text', value: `<ol><li>Navigate to the 'Generate Document' page.</li><li>In the <strong>chat input box</strong>, describe your needs. Be as specific as possible. For example, "Draft a simple non-disclosure agreement between two companies for a new project."</li><li>The AI will generate a document which you can then review, edit, and save.</li></ol><p>You can also start from the 'Templates' page by selecting a pre-made template, which will pre-fill the prompt for you.</p>` }
        ]
      },
      {
        id: 'editor',
        title: 'Using the Document Editor',
        content: [
          { type: 'text', value: `<h2>Using the Document Editor</h2><p>The document editor is where you refine your contracts. It provides a rich set of tools:</p><ul><li><strong>Toolbar:</strong> The toolbar at the top provides standard text formatting options like bold, italics, headings, and lists.</li><li><strong>Editing:</strong> Click the 'Edit' button to enter editing mode. You can type directly into the document.</li><li><strong>Saving:</strong> Click the 'Save' icon to save your changes. This creates a new version in the document's history.</li></ul>`},
          { type: 'text', value: `<h4>Inserting Clauses (Premium)</h4><p>While in editing mode, you can easily insert clauses from your library. Click the <strong>'Insert Clause' button</strong> in the header to open your library, search for the clause you need, and insert it directly into your document.</p>` },
          { type: 'screenshot', component: ClauseInsertionScreenshot },
        ]
      },
       {
        id: 'versioning',
        title: 'Versioning & History',
        content: [
          { type: 'text', value: `<h2>Versioning & History</h2><p>LexiGen automatically saves a new version of your document every time you click 'Save'. You can view, compare, and restore previous versions at any time.</p>`},
          { type: 'screenshot', component: VersioningHelpScreenshot },
          { type: 'text', value: `<ul><li><strong>Access Versions:</strong> Open a document and use the 'Versions' tab in the right-hand utility panel.</li><li><strong>Compare:</strong> Click 'Compare' on an older version to see a redline comparison against the current version.</li><li><strong>Restore:</strong> Click 'Restore' to revert the document's content to that of an older version. This will create a new version history entry.</li></ul>` }
        ]
      },
      {
        id: 'folders',
        title: 'Organizing with Folders',
        content: [
          { type: 'text', value: `<h2>Organizing with Folders</h2><p>The 'Documents' page is your central hub for all saved documents.</p>` },
          { type: 'screenshot', component: FoldersHelpScreenshot },
          { type: 'text', value: `<ol><li><strong>Create a Folder:</strong> Click the <strong>'New Folder' button</strong> to create a new folder.</li><li><strong>Navigate Folders:</strong> Use the <strong>folder list</strong> on the left to filter the documents shown in the main table.</li><li><strong>Move Documents:</strong> From the document list, click the three-dot menu on a document and select 'Move to...' to change its folder.</li></ol>` }
        ]
      },
       {
        id: 'search',
        title: 'Searching Your Workspace',
        content: [
          { type: 'text', value: `<h2>Searching Your Workspace</h2><p>The global search bar at the top of the dashboard allows you to quickly find documents, folders, clauses, and help articles.</p>`},
          { type: 'screenshot', component: SearchHelpScreenshot },
          { type: 'text', value: `<p>Simply start typing your query, and the results will appear in a dropdown, categorized for easy navigation.</p>` }
        ]
      },
    ]
  },
   {
    category: 'Collaboration',
    icon: UsersIcon,
    topics: [
      {
        id: 'clients',
        title: 'Managing Clients',
        content: [
          { type: 'text', value: `<h2>Managing Clients</h2><p>The 'Clients' page allows you to create and manage a central repository of your clients, both companies and individuals.</p>`},
          { type: 'screenshot', component: ClientsHelpScreenshot },
          { type: 'text', value: `<ul><li><strong>Create Clients:</strong> Navigate to 'Workspace' > 'Clients' and click 'New Client'.</li><li><strong>Associate with Documents:</strong> You can link a document to a client when you first save it, or from the document detail page.</li><li><strong>Use in Workflows:</strong> Client information can be used as a condition in your automated workflows (e.g., "If client is Innovate Corp, request approval from Legal").</li></ul>` }
        ]
      },
      {
        id: 'sharing',
        title: 'Sharing & Permissions',
        content: [
          { type: 'text', value: `<h2>Sharing & Permissions</h2><p>You can collaborate on documents with others by inviting them via email. They must have a LexiGen account to access the document.</p>`},
           { type: 'screenshot', component: SharingScreenshot },
          { type: 'text', value: `<ol><li>In the document editor, click the <strong>Share button</strong> in the header.</li><li>Enter the email address of the user you want to invite.</li><li>Choose their permission level: 'Can View' or 'Can Edit'.</li><li>Click 'Invite'.</li></ol><p>Collaborators with 'Edit' access can make changes and add comments. 'View' access is read-only.</p>` }
        ]
      },
      {
        id: 'comments',
        title: 'Commenting & Discussions',
        content: [
          { type: 'text', value: `<h2>Commenting & Discussions</h2><p>Comments are a great way to discuss specific parts of a document with your team.</p><h4>Adding a Comment</h4><ol><li>While in 'Edit' mode, <strong>highlight the text</strong> you want to comment on.</li><li>Click the <strong>comment icon</strong> on the pop-up toolbar.</li></ol>` },
          { type: 'screenshot', component: CommentingScreenshot },
          { type: 'text', value: `<p>This creates a new thread in the right-hand 'Comments' panel. Anyone with access can reply. Once a discussion is finished, click 'Resolve' to hide the thread.</p>` }
        ]
      },
      {
        id: 'signatures',
        title: 'Requesting E-Signatures',
        content: [
          { type: 'text', value: `<h2>Requesting E-Signatures</h2><p>Once a document is finalized, you can send it for legally binding e-signatures directly from the platform.</p>`},
           { type: 'screenshot', component: SignaturesHelpScreenshot },
          { type: 'text', value: `<ul><li><strong>Request Signatures:</strong> From the document editor, click the 'Signature' icon. Add the email addresses and roles for each signer.</li><li><strong>Tracking:</strong> Once sent, the document is locked for editing. You can track the status of each signature in the 'Signatures' tab of the utility panel.</li><li><strong>Completion:</strong> When all parties have signed, the document status is automatically updated to 'Completed'.</li></ul>` }
        ]
      },
      {
        id: 'team',
        title: 'Team Management',
        content: [
          { type: 'text', value: `<h2>Team Management (Premium)</h2><p>On Premium and Enterprise plans, you can manage a team from the 'Team' page.</p>` },
          { type: 'screenshot', component: TeamManagementHelpScreenshot },
          { type: 'text', value: `<ul><li>Click the <strong>'Invite Member' button</strong> to add new users.</li><li>You can assign roles like 'Admin' or 'Member'. 'Admins' can also manage team members and settings.</li><li>The team owner can remove members or change their roles from the main table.</li></ul>` }
        ]
      },
    ]
  },
  {
    category: 'AI & Automation',
    icon: WandIcon,
    topics: [
      {
        id: 'analysis',
        title: 'Analyzing Documents',
        content: [
          { type: 'text', value: `<h2>Analyzing a Document (Premium)</h2><p>The 'Analyze Document' feature helps you understand third-party contracts by identifying risks and summarizing key clauses.</p>` },
          { type: 'screenshot', component: AnalysisHelpScreenshot },
          { type: 'text', value: `<ol><li>Navigate to the 'Analyze Document' page.</li><li><strong>Paste the full text</strong> of the legal document into the text area, or use the <strong>'Upload File' button</strong>.</li><li>Click 'Analyze Document'.</li></ol><p>The AI will review the text and provide a structured analysis, including a summary, potential risks, and actionable suggestions.</p>` }
        ]
      },
      {
        id: 'clauselib',
        title: 'Clause Library & Suggestions',
        content: [
          { type: 'text', value: `<h2>Clause Library (Premium)</h2><p>The 'Clause Library' allows you to save and reuse your own pre-approved legal clauses, ensuring consistency across documents.</p>` },
          { type: 'screenshot', component: ClauseLibraryHelpScreenshot },
          { type: 'text', value: `<ul><li><strong>Create a Clause:</strong> Go to the library and click 'New Clause'.</li><li><strong>Save from Editor:</strong> When editing, you can highlight text and use the pop-up toolbar to 'Save as Clause'.</li><li><strong>Insert a Clause:</strong> When editing, click the 'Insert Clause' button to search and add clauses to your document.</li><li><strong>AI Suggestions:</strong> In the document editor's utility panel, the 'AI Suggestions' tool can analyze your draft and recommend additional clauses to include.</li></ul>` }
        ]
      },
       {
        id: 'lifecycle',
        title: 'Contract Lifecycle',
        content: [
          { type: 'text', value: `<h2>Contract Lifecycle (Premium)</h2><p>The 'Lifecycle' page gives you a high-level view of your contract pipeline using a Kanban board.</p>` },
          { type: 'screenshot', component: LifecycleHelpScreenshot },
          { type: 'text', value: `<p>You can <strong>drag and drop documents</strong> between statuses (e.g., from 'Draft' to 'Out for Signature'). When a document is 'Completed', our AI automatically extracts key dates and obligations.</p>` }
        ]
      },
       {
        id: 'obligations',
        title: 'Obligation Tracking',
        content: [
          { type: 'text', value: `<h2>Obligation Tracking (Premium)</h2><p>Never miss a contractual deadline. When a contract is marked 'Completed', our AI automatically extracts key obligations and their due dates.</p>` },
          { type: 'screenshot', component: ObligationsHelpScreenshot },
          { type: 'text', value: `<p>You can view all upcoming deadlines from the 'Obligations' page in your workspace. You can also see a document's specific obligations in the utility panel of the document editor.</p>` }
        ]
      },
      {
        id: 'workflows',
        title: 'Automating with Workflows',
        content: [
          { type: 'text', value: `<h2>Automating with Workflows (Premium)</h2><p>The 'Workflows' page allows you to build powerful, multi-step automations for your contract processes.</p>` },
          { type: 'screenshot', component: WorkflowsHelpScreenshot },
          { type: 'text', value: `<p>Use the visual builder to create custom flows. For example, you can design a workflow that automatically requests approval from the legal team for any contract with a value over $10,000.</p>` }
        ]
      },
    ]
  },
   {
    category: 'Account & Settings',
    icon: SettingsIcon,
    topics: [
      { id: 'subscription', title: 'Managing Your Subscription', content: `<h2>Managing Your Subscription</h2><p>You can view and manage your subscription plan from the 'Subscription' page.</p><ul><li>See your current plan and its features.</li><li>Compare plans and upgrade if needed.</li><li>The platform will simulate an upgrade process for demonstration purposes.</li></ul>` },
      {
        id: 'settings',
        title: 'Account Settings',
        content: [
          { type: 'text', value: `<h2>Account Settings</h2><p>The 'Settings' page allows you to manage your account details via a clear, tabbed interface.</p>` },
          { type: 'screenshot', component: SettingsHelpScreenshot },
          { type: 'text', value: `<ul><li><strong>Profile:</strong> Update your name, username, and professional details.</li><li><strong>Appearance:</strong> Choose between light, dark, and system themes.</li><li><strong>Notifications:</strong> Control which email notifications you receive.</li><li><strong>Security:</strong> Change your password or delete your account.</li></ul>` }
        ]
      },
      { id: 'notifications', title: 'Notifications', content: `<h2>Notifications</h2><p>You can view your most recent notifications by clicking the bell icon in the header. The 'Notifications' page under your workspace provides a complete history of all your notifications.</p>` },
      {
        id: 'api-sso',
        title: 'API & SSO',
        content: [
          { type: 'text', value: `<h2>API & SSO (Enterprise)</h2><p>For Enterprise customers, additional tabs appear in Settings for advanced configuration.</p>` },
          { type: 'screenshot', component: ApiSsoHelpScreenshot },
          { type: 'text', value: `<ul><li><strong>API & Integrations:</strong> Generate and manage API keys to connect LexiGen to other software.</li><li><strong>Security (SSO):</strong> Configure Single Sign-On to allow your team to log in using your company's identity provider.</li></ul>` }
        ]
      },
    ]
  },
  {
    category: 'Integrations',
    icon: IntegrationsIcon,
    topics: [
      {
        id: 'integrations-setup',
        title: 'Integrations: Setup and Data Mapping',
        content: [
          { type: 'text', value: `<h2>Summary</h2><p>Integrating LexiGen with third-party applications allows you to automate critical business workflows by synchronizing data between systems. This can significantly reduce manual data entry, prevent errors, and accelerate processes like invoicing, client onboarding, and financial reporting.</p><p>This guide provides a comprehensive overview of how to set up, configure, and manage these integrations.</p><h2>Prerequisites</h2><p>Before you begin, ensure you meet the following requirements:</p><ul><li><strong>Plan:</strong> You must be on the <strong>Enterprise</strong> plan.</li><li><strong>Role:</strong> You must have an <strong>Admin</strong> role within your team.</li><li><strong>API Keys:</strong> You need permissions to view and generate API keys. See our guide on <a href="#/help/api-sso">API & SSO</a> for more details.</li><li><strong>Third-Party Account:</strong> You need an active account with administrative access to the third-party application you wish to connect.</li><li><strong>OAuth Scopes:</strong> During authorization, you will be asked to grant permissions. LexiGen requires the following scopes: <code>{{OAUTH_SCOPE_READ_WRITE}}</code>, <code>{{OAUTH_SCOPE_INVOICING}}</code>.</li></ul><h2>Supported Systems</h2><p>LexiGen currently supports direct integrations with the following systems:</p><ul><li>QuickBooks Online</li><li>Zoho Books <code>(Coming Soon)</code></li><li>Moneybird <code>(Coming Soon)</code></li><li>{{THIRD_PARTY_SYSTEM_4}} <code>(Coming Soon)</code></li></ul><div class="p-4 bg-zinc-100 dark:bg-zinc-800/50 rounded-lg my-4"><p><strong>Tip:</strong> You can connect to thousands of other apps using our API keys with an automation platform like Zapier or Make.</p></div><h2>Data Mapping</h2><p>Data is synchronized between LexiGen and the connected application according to the mappings below.</p><h4>Clients / Customers</h4><div class="overflow-x-auto"><table class="w-full"><thead><tr><th class="p-2 text-left">Source (LexiGen)</th><th class="p-2 text-left">Mapped Field (Third-Party)</th><th class="p-2 text-left">Direction</th><th class="p-2 text-left">Sync Frequency</th><th class="p-2 text-left">Conflict Rule</th></tr></thead><tbody><tr><td class="p-2"><code>client.name</code></td><td class="p-2"><code>Customer.DisplayName</code></td><td class="p-2">LexiGen -&gt;</td><td class="p-2">On Create/Update</td><td class="p-2">Source of Truth: LexiGen</td></tr><tr><td class="p-2"><code>client.email</code></td><td class="p-2"><code>Customer.PrimaryEmailAddr</code></td><td class="p-2">Bidirectional</td><td class="p-2">On Update</td><td class="p-2">Most Recently Updated</td></tr><tr><td class="p-2"><code>client.address</code></td><td class="p-2"><code>Customer.BillAddr</code></td><td class="p-2">LexiGen -&gt;</td><td class="p-2">On Create/Update</td><td class="p-2">Source of Truth: LexiGen</td></tr></tbody></table></div><h4>Documents / Contracts</h4><div class="overflow-x-auto"><table class="w-full"><thead><tr><th class="p-2 text-left">Source (LexiGen)</th><th class="p-2 text-left">Mapped Field (Third-Party)</th><th class="p-2 text-left">Direction</th><th class="p-2 text-left">Sync Frequency</th><th class="p-2 text-left">Conflict Rule</th></tr></thead><tbody><tr><td class="p-2"><code>document.name</code></td><td class="p-2"><code>Estimate.DocNumber</code> or <code>Note.Title</code></td><td class="p-2">LexiGen -&gt;</td><td class="p-2">On Status Change to <code>Completed</code></td><td class="p-2">N/A (Creates new record)</td></tr><tr><td class="p-2"><code>document.value</code></td><td class="p-2"><code>Invoice.TotalAmt</code></td><td class="p-2">LexiGen -&gt;</td><td class="p-2">On Status Change to <code>Completed</code></td><td class="p-2">N/A (Creates new record)</td></tr><tr><td class="p-2"><code>document.status</code></td><td class="p-2">Triggers Invoice Creation</td><td class="p-2">N/A</td><td class="p-2">On Event</td><td class="p-2">N/A</td></tr></tbody></table></div><h2>Security Considerations</h2><ul><li><strong>Data in Transit:</strong> All data transferred between LexiGen and third-party services is encrypted using TLS 1.2 or higher.</li><li><strong>Authentication Tokens:</strong> OAuth tokens and API keys are encrypted at rest using AES-256.</li><li><strong>Token Revocation:</strong> You can revoke an integration's access at any time from the <code>{{MENU_PATH}}</code> page. This will immediately invalidate the stored tokens.</li><li><strong>Rate Limits:</strong> API calls to third-party services are subject to their respective rate limits. LexiGen uses an intelligent queueing system to manage this, but heavy usage may result in temporary delays. For more details, see our developer documentation on <a href="/dev/api-limits">API rate limits</a>.</li></ul>` },
          { type: 'text', value: '<h2>Step-by-Step Setup</h2><p>Follow these steps to connect a new application.</p>' },
          { type: 'screenshot', component: IntegrationsHelpScreenshot },
          { type: 'text', value: `<ol><li><strong>Navigate to the Integrations Page.</strong><br/><strong>Action:</strong> Click on the \`Integrations\` menu item.<br/><strong>UI Path:</strong> <code>{{MENU_PATH}}</code><br/><strong>Expected Result:</strong> You will see a gallery of available applications to connect.<br/><strong>Recovery Tip:</strong> If you don't see the \`Integrations\` option, please verify you are on the Enterprise plan and have Admin permissions.</li><li><strong>Select and Connect an Application.</strong><br/><strong>Action:</strong> Find the application you wish to integrate (e.g., QuickBooks Online) and click the <code>{{BUTTON_LABEL_CONNECT}}</code> button.<br/><strong>UI Path:</strong> <code>{{MENU_PATH}}</code> -&gt; Click <code>{{BUTTON_LABEL_CONNECT}}</code><br/><strong>Expected Result:</strong> You will be redirected to the third-party application's authorization screen in a new window or tab.<br/><strong>Recovery Tip:</strong> If the new window does not open, check if your browser is blocking pop-ups.</li><li><strong>Authorize the Connection.</strong><br/><strong>Action:</strong> Log in to your third-party account if prompted, and approve the requested permissions for LexiGen.<br/><strong>UI Path:</strong> Third-party authorization screen.<br/><strong>Expected Result:</strong> The window will close, and you will be returned to LexiGen. The application will now show a "Connected" status.<br/><strong>Recovery Tip:</strong> If authorization fails, ensure you are using an account with administrative privileges in the third-party application.</li><li><strong>Configure Settings and Field Mapping.</strong><br/><strong>Action:</strong> Once connected, click on the <code>{{BUTTON_LABEL_CONFIGURE}}</code> button to open the integration's settings. Map the fields from LexiGen to the corresponding fields in the connected application.<br/><strong>UI Path:</strong> <code>{{MENU_PATH}}</code> -&gt; Click <code>{{BUTTON_LABEL_CONFIGURE}}</code><br/><strong>Expected Result:</strong> A modal or page appears with dropdowns for mapping fields like Client Name, Document Value, etc.<br/><strong>Recovery Tip:</strong> If a required field is missing, you may need to configure it in the third-party application first.</li><li><strong>Enable and Test the Sync.</strong><br/><strong>Action:</strong> After configuring the mappings, toggle the <code>{{TOGGLE_LABEL_ENABLE_SYNC}}</code> switch to "On" and click <code>{{BUTTON_LABEL_SAVE_SETTINGS}}</code>. Then, use the <code>{{BUTTON_LABEL_TEST_SYNC}}</code> button.<br/><strong>UI Path:</strong> Integration configuration modal.<br/><strong>Expected Result:</strong> A success message indicates that a test record was successfully created in the third-party system.<br/><strong>Recovery Tip:</strong> If the test fails, double-check your field mappings for any errors or missing required fields.</li></ol><h2>Verification Checklist</h2><ul><li>[ ] Is the application status shown as "Connected" on the \`Integrations\` page?</li><li>[ ] Did the test sync create a record in the third-party application as expected?</li><li>[ ] Are all required fields in the mapping configuration correctly assigned?</li><li>[ ] Is the <code>{{TOGGLE_LABEL_ENABLE_SYNC}}</code> switch in the "On" position?</li></ul><h2>Troubleshooting</h2><div class="overflow-x-auto"><table class="w-full"><thead><tr><th class="p-2 text-left">Error Message</th><th class="p-2 text-left">Cause & Resolution</th></tr></thead><tbody><tr><td class="p-2"><code>Connection Failed: 401 Unauthorized</code></td><td class="p-2">The credentials or token are invalid. <strong>Fix:</strong> Disconnect and reconnect the application to re-authorize.</td></tr><tr><td class="p-2"><code>Sync Failed: Missing required field {{FIELD_NAME}}</code></td><td class="p-2">The field mapping is incomplete. <strong>Fix:</strong> Go to the integration's configuration and ensure <code>{{FIELD_NAME}}</code> is mapped.</td></tr><tr><td class="p-2"><code>429 Too Many Requests</code></td><td class="p-2">The third-party API rate limit was exceeded. <strong>Fix:</strong> This is usually temporary. The sync will automatically retry after a short delay.</td></tr></tbody></table></div><h2>Frequently Asked Questions (FAQs)</h2><ol><li><strong>How often does data sync?</strong><br/>Data syncs based on specific events (e.g., when a document's status changes to \`Completed\`) and during a nightly reconciliation process. See the Data Mapping tables for details.</li><li><strong>Can I disable an integration temporarily?</strong><br/>Yes. Navigate to <code>{{MENU_PATH}}</code>, click <code>{{BUTTON_LABEL_CONFIGURE}}</code> on the integration, and turn off the <code>{{TOGGLE_LABEL_ENABLE_SYNC}}</code> switch.</li><li><strong>What happens if I disconnect an integration?</strong><br/>All data syncing will stop immediately. LexiGen will securely delete the stored authentication tokens. No previously synced data will be removed from either system.</li></ol><h2>Change Log</h2><ul><li><strong>October 26, 2023:</strong> Initial version of the integrations guide published.</li></ul>` },
        ]
      },
    ]
  },
];


export const INITIAL_PUBLIC_TEMPLATES: Template[] = [
  {
    id: 'nda-1',
    title: 'Mutual Non-Disclosure Agreement (NDA)',
    description: 'A standard agreement for two parties who will be sharing confidential information with each other.',
    category: 'Business Agreements',
    prompt: 'Draft a standard, mutual Non-Disclosure Agreement (NDA) between two parties. Include clauses for definition of confidential information, obligations of receiving party, exclusions, term, and jurisdiction.',
    requiredPlan: 'Registered User',
  },
  {
    id: 'freelance-1',
    title: 'Freelance Graphic Design Contract',
    description: 'A contract for hiring a freelance graphic designer, covering scope of work, payment terms, and intellectual property.',
    category: 'Freelance & Consulting',
    prompt: 'Create a freelance contract for a graphic designer. The client will pay a fixed fee. The contract should cover project scope, deliverables, payment schedule, intellectual property rights transfer upon final payment, and a section on client revisions.',
    requiredPlan: 'Registered User',
  },
  {
    id: 'lease-1',
    title: 'Residential Lease Agreement',
    description: 'A basic agreement for leasing a residential property, outlining terms, rent, and responsibilities.',
    category: 'Real Estate',
    prompt: 'Generate a basic residential lease agreement. It should include fields for landlord and tenant names, property address, lease term, monthly rent amount, security deposit, and rules regarding property use.',
    requiredPlan: 'Registered User',
  },
  {
    id: 'privacy-1',
    title: 'Website Privacy Policy',
    description: 'A standard privacy policy for a website that collects user data, compliant with common regulations.',
    category: 'Website & Online',
    prompt: 'Draft a comprehensive website privacy policy. It must cover what data is collected (e.g., personal information, cookies), how it is used, data sharing practices, user rights, and contact information for privacy-related inquiries.',
    requiredPlan: 'Registered User',
  },
  {
    id: 'saas-1',
    title: 'SaaS Terms of Service',
    description: 'A comprehensive Terms of Service agreement for a Software-as-a-Service (SaaS) product.',
    category: 'Business Agreements',
    prompt: 'Generate a detailed Terms of Service (ToS) for a SaaS application. Include clauses on user accounts, subscription terms, acceptable use policy, intellectual property ownership, limitation of liability, and termination.',
    requiredPlan: 'Premium',
  },
  {
    id: 'consulting-1',
    title: 'Consulting Agreement',
    description: 'A detailed agreement for providing professional consulting services to a client.',
    category: 'Freelance & Consulting',
    prompt: 'Create a professional consulting agreement. Specify the scope of services, consultant and client responsibilities, compensation structure (retainer plus hourly), confidentiality, and term of the agreement.',
    requiredPlan: 'Premium',
  },
  {
    id: 'employment-1',
    title: 'Employment Offer Letter',
    description: 'A formal offer of employment for a new full-time employee, including salary, benefits, and start date.',
    category: 'Human Resources',
    prompt: 'Draft a formal employment offer letter for a full-time salaried position. Include sections for job title, duties, start date, salary, bonus structure, benefits overview (health insurance, 401k), and an at-will employment statement.',
    requiredPlan: 'Registered User',
  },
  {
    id: 'photo-release-1',
    title: 'Photography Model Release Form',
    description: 'A form granting a photographer the right to use photos of a model for commercial purposes.',
    category: 'Creative',
    prompt: 'Generate a photography model release form. The model grants the photographer rights to use their likeness in photos for any lawful purpose, including advertising. Include fields for model name, photographer name, date, and signatures.',
    requiredPlan: 'Registered User',
  },
  {
    id: 'commercial-lease-1',
    title: 'Commercial Lease Agreement',
    description: 'An agreement for leasing commercial property, such as an office or retail space.',
    category: 'Real Estate',
    prompt: 'Draft a commercial lease agreement for an office space. Include clauses for lease term, base rent, operating expense responsibilities (Triple Net Lease), permitted use of premises, maintenance obligations, and insurance requirements.',
    requiredPlan: 'Premium',
  },
  {
    id: 'influencer-1',
    title: 'Influencer Marketing Agreement',
    description: 'A contract between a brand and a social media influencer for a marketing campaign.',
    category: 'Creative',
    prompt: 'Create an influencer marketing agreement. The brand will provide free products and a flat fee for a specific number of social media posts. The contract should define the content requirements, posting schedule, FTC disclosure guidelines, content ownership, and payment terms.',
    requiredPlan: 'Premium',
  },
   {
    id: 'mna-1',
    title: 'Merger and Acquisition (M&A) LOI',
    description: 'A non-binding Letter of Intent for a potential merger or acquisition between two companies.',
    category: 'Business Agreements',
    prompt: 'Draft a non-binding Letter of Intent (LOI) for a potential acquisition of a smaller tech company by a larger one. Include a proposed purchase price, structure of the deal (stock purchase), key conditions, a due diligence period, and an exclusivity (no-shop) clause.',
    requiredPlan: 'Premium',
  },
   {
    id: 'dev-contract-1',
    title: 'Software Development Agreement',
    description: 'A contract for building a custom software application for a client.',
    category: 'Freelance & Consulting',
    prompt: 'Generate a detailed software development agreement. The project will be billed on a time and materials basis. The contract must include: development services, project timeline and milestones, acceptance testing procedures, intellectual property rights, confidentiality, and post-launch support terms.',
    requiredPlan: 'Premium',
  },
];

export const PRECONFIGURED_WORKFLOWS: Omit<WorkflowTemplate, 'id'>[] = [
    {
        name: 'Sales Contract Approval (> $10k)',
// FIX: Added missing status property to the workflow template.
        status: 'inactive',
        nodes: [
            { id: 'trigger-1', type: 'trigger', position: { x: 275, y: 50 }, data: { label: 'On Approval Request', triggerType: 'approval-requested' } },
            { id: 'condition-1', type: 'condition', position: { x: 275, y: 200 }, data: { label: 'Value > $10,000', conditionField: 'value', conditionOperator: '>', conditionValue: 10000 } },
            { id: 'approval-manager', type: 'approval', position: { x: 50, y: 350 }, data: { label: 'Sales Manager Approval', approverEmail: '<EMAIL>' } },
            { id: 'approval-legal-1', type: 'approval', position: { x: 275, y: 500 }, data: { label: 'Legal Approval', approverEmail: '<EMAIL>' } },
            { id: 'approval-legal-2', type: 'approval', position: { x: 500, y: 350 }, data: { label: 'Legal Approval', approverEmail: '<EMAIL>' } },
        ],
        edges: [
            { id: 'e-t1-c1', source: 'trigger-1', target: 'condition-1' },
            { id: 'e-c1-am', source: 'condition-1', sourceHandle: 'true', target: 'approval-manager' },
            { id: 'e-c1-al2', source: 'condition-1', sourceHandle: 'false', target: 'approval-legal-2' },
            { id: 'e-am-al1', source: 'approval-manager', target: 'approval-legal-1' },
        ],
    },
    {
        name: 'Vendor Onboarding',
// FIX: Added missing status property to the workflow template.
        status: 'inactive',
        nodes: [
            { id: 'trigger-1', type: 'trigger', position: { x: 50, y: 50 }, data: { label: 'On Document Creation', triggerType: 'document-created' } },
            { id: 'approval-1', type: 'approval', position: { x: 50, y: 200 }, data: { label: 'Security Team Approval', approverEmail: '<EMAIL>' } },
            { id: 'update-1', type: 'update-field', position: { x: 50, y: 350 }, data: { label: 'Set Status to In Review', updateField: 'status', updateValue: 'in-review' } },
        ],
        edges: [
            { id: 'e-t1-a1', source: 'trigger-1', target: 'update-1' },
            { id: 'e-u1-a1', source: 'update-1', target: 'approval-1' },
        ],
    },
     {
        name: 'Auto-Archive Completed Contracts',
// FIX: Added missing status property to the workflow template.
        status: 'inactive',
        nodes: [
            { id: 'trigger-1', type: 'trigger', position: { x: 50, y: 50 }, data: { label: 'On Status Change', triggerType: 'status-changed' } },
            { id: 'condition-1', type: 'condition', position: { x: 50, y: 200 }, data: { label: 'Status is "Completed"', conditionField: 'status', conditionOperator: 'is', conditionValue: 'completed' } },
            { id: 'delay-1', type: 'delay', position: { x: 50, y: 350 }, data: { label: 'Wait for 30 days', delayDays: 30 } },
            { id: 'update-1', type: 'update-field', position: { x: 50, y: 500 }, data: { label: 'Set Status to Archived', updateField: 'status', updateValue: 'archived' } },
        ],
        edges: [
            { id: 'e-t1-c1', source: 'trigger-1', target: 'condition-1' },
            { id: 'e-c1-d1', source: 'condition-1', sourceHandle: 'true', target: 'delay-1' },
            { id: 'e-d1-u1', source: 'delay-1', target: 'update-1' },
        ],
    },
    {
        name: 'HR Offer Letter Process',
// FIX: Added missing status property to the workflow template.
        status: 'inactive',
        nodes: [
            { id: 'n1', type: 'trigger', position: { x: 50, y: 50 }, data: { label: 'On Approval Request', triggerType: 'approval-requested' } },
            { id: 'n2', type: 'condition', position: { x: 50, y: 200 }, data: { label: "If Folder is 'HR Offers'", conditionField: 'folderId', conditionOperator: 'is', conditionValue: 'folder_hr_offers_placeholder' } },
            { id: 'n3', type: 'approval', position: { x: 50, y: 350 }, data: { label: 'Hiring Manager Approval', approverEmail: '<EMAIL>' } },
            { id: 'n4', type: 'approval', position: { x: 50, y: 500 }, data: { label: 'HR Director Approval', approverEmail: '<EMAIL>' } },
        ],
        edges: [
            { id: 'e1-2', source: 'n1', target: 'n2' },
            { id: 'e2-3', source: 'n2', sourceHandle: 'true', target: 'n3' },
            { id: 'e3-4', source: 'n3', target: 'n4' },
        ],
    },
    {
        name: 'High-Value Sales Alert',
// FIX: Added missing status property to the workflow template.
        status: 'inactive',
        nodes: [
            { id: 'n1', type: 'trigger', position: { x: 50, y: 50 }, data: { label: 'On Document Creation', triggerType: 'document-created' } },
            { id: 'n2', type: 'condition', position: { x: 50, y: 200 }, data: { label: 'If Value > $50,000', conditionField: 'value', conditionOperator: '>', conditionValue: 50000 } },
            { id: 'n3', type: 'notification', position: { x: 50, y: 350 }, data: { label: 'Notify Sales Director', notificationRecipient: '<EMAIL>' } },
        ],
        edges: [
            { id: 'e1-2', source: 'n1', target: 'n2' },
            { id: 'e2-3', source: 'n2', sourceHandle: 'true', target: 'n3' },
        ],
    },
    {
        name: 'NDA Auto-Filing & Sharing',
// FIX: Added missing status property to the workflow template.
        status: 'inactive',
        nodes: [
            { id: 'n1', type: 'trigger', position: { x: 50, y: 50 }, data: { label: 'On Document Creation', triggerType: 'document-created' } },
            { id: 'n2', type: 'condition', position: { x: 50, y: 200 }, data: { label: "If Name contains 'NDA'", conditionField: 'name', conditionOperator: 'contains', conditionValue: 'NDA' } },
            { id: 'n3', type: 'move-to-folder', position: { x: 50, y: 350 }, data: { label: "Move to 'NDAs' Folder", targetFolderId: 'folder_nda_placeholder' } },
            { id: 'n4', type: 'add-collaborator', position: { x: 50, y: 500 }, data: { label: 'Share with Legal', collaboratorEmail: '<EMAIL>', collaboratorPermission: 'view' } },
        ],
        edges: [
            { id: 'e1-2', source: 'n1', target: 'n2' },
            { id: 'e2-3', source: 'n2', sourceHandle: 'true', target: 'n3' },
            { id: 'e3-4', source: 'n3', target: 'n4' },
        ],
    },
    {
        name: 'Stale Draft Reminder',
// FIX: Added missing status property to the workflow template.
        status: 'inactive',
        nodes: [
            { id: 'n1', type: 'trigger', position: { x: 50, y: 50 }, data: { label: 'On Document Creation', triggerType: 'document-created' } },
            { id: 'n2', type: 'delay', position: { x: 50, y: 200 }, data: { label: 'Wait for 14 Days', delayDays: 14 } },
            { id: 'n3', type: 'condition', position: { x: 50, y: 350 }, data: { label: "If Status is 'draft'", conditionField: 'status', conditionOperator: 'is', conditionValue: 'draft' } },
            { id: 'n4', type: 'notification', position: { x: 50, y: 500 }, data: { label: 'Notify Document Owner', notificationRecipient: '<EMAIL>' } },
        ],
        edges: [
            { id: 'e1-2', source: 'n1', target: 'n2' },
            { id: 'e2-3', source: 'n2', target: 'n3' },
            { id: 'e3-4', source: 'n3', sourceHandle: 'true', target: 'n4' },
        ],
    },
    {
        name: 'Procurement Contract Review',
// FIX: Added missing status property to the workflow template.
        status: 'inactive',
        nodes: [
            { id: 'n1', type: 'trigger', position: { x: 50, y: 50 }, data: { label: 'On Approval Request', triggerType: 'approval-requested' } },
            { id: 'n2', type: 'approval', position: { x: 50, y: 200 }, data: { label: 'Procurement Head Approval', approverEmail: '<EMAIL>' } },
            { id: 'n3', type: 'approval', position: { x: 50, y: 350 }, data: { label: 'Legal Approval', approverEmail: '<EMAIL>' } },
        ],
        edges: [
            { id: 'e1-2', source: 'n1', target: 'n2' },
            { id: 'e2-3', source: 'n2', target: 'n3' },
        ]
    },
    {
        name: 'Marketing Agreement Auto-Tag',
// FIX: Added missing status property to the workflow template.
        status: 'inactive',
        nodes: [
            { id: 'n1', type: 'trigger', position: { x: 50, y: 50 }, data: { label: 'On Document Creation', triggerType: 'document-created' } },
            { id: 'n2', type: 'condition', position: { x: 50, y: 200 }, data: { label: "If Name contains 'Marketing'", conditionField: 'name', conditionOperator: 'contains', conditionValue: 'Marketing' } },
            { id: 'n3', type: 'add-tag', position: { x: 50, y: 350 }, data: { label: "Add 'Marketing' Tag", tagName: 'Marketing' } },
        ],
        edges: [
            { id: 'e1-2', source: 'n1', target: 'n2' },
            { id: 'e2-3', source: 'n2', sourceHandle: 'true', target: 'n3' },
        ],
    },
    {
        name: 'Client Onboarding Kickoff',
// FIX: Added missing status property to the workflow template.
        status: 'inactive',
        nodes: [
            { id: 'n1', type: 'trigger', position: { x: 50, y: 50 }, data: { label: 'On Status Change', triggerType: 'status-changed' } },
            { id: 'n2', type: 'condition', position: { x: 50, y: 200 }, data: { label: "If Status is 'completed'", conditionField: 'status', conditionOperator: 'is', conditionValue: 'completed' } },
            { id: 'n3', type: 'add-collaborator', position: { x: 50, y: 350 }, data: { label: 'Add Project Manager', collaboratorEmail: '<EMAIL>', collaboratorPermission: 'edit' } },
            { id: 'n4', type: 'notification', position: { x: 50, y: 500 }, data: { label: 'Notify Kickoff Team', notificationRecipient: '<EMAIL>' } },
        ],
        edges: [
            { id: 'e1-2', source: 'n1', target: 'n2' },
            { id: 'e2-3', source: 'n2', sourceHandle: 'true', target: 'n3' },
            { id: 'e3-4', source: 'n3', target: 'n4' },
        ],
    }
];

export const INITIAL_TERMS_CONTENT = `
<p>Please read these Terms and Conditions ("Terms", "Terms and Conditions") carefully before using the LexiGen application (the "Service") operated by LexiGen ("us", "we", or "our").</p>
<p>Your access to and use of the Service is conditioned on your acceptance of and compliance with these Terms. These Terms apply to all visitors, users, and others who access or use the Service.</p>
<p><strong>By accessing or using the Service you agree to be bound by these Terms. If you disagree with any part of the terms then you may not access the Service.</strong></p>
<h2>1. AI-Generated Content and Legal Disclaimer</h2>
<p>THE SERVICE USES ARTIFICIAL INTELLIGENCE TO GENERATE DRAFTS OF LEGAL DOCUMENTS AND CLAUSES ("AI CONTENT"). YOU ACKNOWLEDGE AND AGREE TO THE FOLLOWING:</p>
<ul>
    <li><strong>Not Legal Advice:</strong> AI Content is provided for informational purposes only and does not constitute legal advice. The Service is not a law firm and does not provide legal services.</li>
    <li><strong>No Attorney-Client Relationship:</strong> Your use of the Service does not create an attorney-client relationship between you and LexiGen.</li>
    <li><strong>Consult a Qualified Attorney:</strong> The AI Content is a starting point and may not be appropriate for your specific situation. It may contain errors, omissions, or be outdated. You are solely responsible for reviewing, editing, and ensuring the legal accuracy and enforceability of any document you create. We strongly recommend you consult with a qualified, licensed attorney before using or relying on any AI Content.</li>
    <li><strong>No Warranty:</strong> We make no warranties or representations regarding the accuracy, completeness, or suitability of the AI Content for any purpose.</li>
</ul>
<h2>2. User Accounts</h2>
<p>When you create an account with us, you must provide information that is accurate, complete, and current at all times. Failure to do so constitutes a breach of the Terms, which may result in immediate termination of your account on our Service.</p>
<p>You are responsible for safeguarding the password that you use to access the Service and for any activities or actions under your password. You agree not to disclose your password to any third party.</p>
<h2>3. Subscriptions and Payments</h2>
<p>Some parts of the Service are billed on a subscription basis ("Subscription(s)"). You will be billed in advance on a recurring and periodic basis ("Billing Cycle").</p>
<p>We reserve the right to change our subscription plans or adjust pricing for our services in any manner and at any time as we may determine in our sole and absolute discretion.</p>
<h2>4. Intellectual Property</h2>
<p>The Service and its original content (excluding content provided by users), features, and functionality are and will remain the exclusive property of LexiGen. The user retains all ownership rights to the final documents they create using the Service, subject to the disclaimers in Section 1.</p>
<h2>5. Prohibited Uses</h2>
<p>You agree not to use the Service for any unlawful purpose or to solicit others to perform or participate in any unlawful acts. You are prohibited from using the site or its content to infringe upon or violate our intellectual property rights or the intellectual property rights of others.</p>
<h2>6. Limitation Of Liability</h2>
<p>IN NO EVENT SHALL LEXIGEN, NOR ITS DIRECTORS, EMPLOYEES, PARTNERS, AGENTS, SUPPLIERS, OR AFFILIATES, BE LIABLE FOR ANY INDIRECT, INCIDENTAL, SPECIAL, CONSEQUENTIAL OR PUNITIVE DAMAGES, INCLUDING WITHOUT LIMITATION, LOSS OF PROFITS, DATA, USE, GOODWILL, OR OTHER INTANGIBLE LOSSES, RESULTING FROM YOUR ACCESS TO OR USE OF OR INABILITY TO ACCESS OR USE THE SERVICE OR ANY AI CONTENT.</p>
<h2>7. Termination</h2>
<p>We may terminate or suspend your account immediately, without prior notice or liability, for any reason whatsoever, including without limitation if you breach the Terms.</p>
<h2>8. Changes To Terms</h2>
<p>We reserve the right, at our sole discretion, to modify or replace these Terms at any time. We will provide notice of any changes by posting the new Terms and Conditions on this page.</p>
<h2>Contact Us</h2>
<p>If you have any questions about these Terms, please contact <NAME_EMAIL>.</p>
`;

export const INITIAL_PRIVACY_CONTENT = `
<p>This Privacy Policy describes Our policies and procedures on the collection, use and disclosure of Your information when You use the Service and tells You about Your privacy rights and how the law protects You.</p>
<p>We use Your Personal data to provide and improve the Service. By using the Service, You agree to the collection and use of information in accordance with this Privacy Policy.</p>
<h2>Collecting and Using Your Personal Data</h2>
<h3>Types of Data Collected</h3>
<h4>Personal Data</h4>
<p>While using Our Service, We may ask You to provide Us with certain personally identifiable information that can be used to contact or identify You. Personally identifiable information may include, but is not limited to:</p>
<ul>
    <li>Email address</li>
    <li>First name and last name</li>
    <li>Usage Data</li>
</ul>
<h4>Usage Data</h4>
<p>Usage Data is collected automatically when using the Service. Usage Data may include information such as Your Device's Internet Protocol address (e.g. IP address), browser type, browser version, the pages of our Service that You visit, the time and date of Your visit, the time spent on those pages, unique device identifiers and other diagnostic data.</p>
<h3>Use of Your Personal Data</h3>
<p>The Company may use Personal Data for the following purposes:</p>
<ul>
    <li>To provide and maintain our Service, including to monitor the usage of our Service.</li>
    <li>To manage Your Account: to manage Your registration as a user of the Service.</li>
    <li>For the performance of a contract: the development, compliance and undertaking of the purchase contract for the products, items or services You have purchased or of any other contract with Us through the Service.</li>
    <li>To contact You: To contact You by email regarding updates or informative communications related to the functionalities, products or contracted services, including the security updates, when necessary or reasonable for their implementation.</li>
</ul>
<h2>Security of Your Personal Data</h2>
<p>The security of Your Personal Data is important to Us, but remember that no method of transmission over the Internet, or method of electronic storage is 100% secure. While We strive to use commercially acceptable means to protect Your Personal Data, We cannot guarantee its absolute security.</p>
<h2>Changes to this Privacy Policy</h2>
<p>We may update Our Privacy Policy from time to time. We will notify You of any changes by posting the new Privacy Policy on this page.</p>
<h2>Contact Us</h2>
<p>If you have any questions about this Privacy Policy, you can contact us at: <EMAIL></p>
`;
