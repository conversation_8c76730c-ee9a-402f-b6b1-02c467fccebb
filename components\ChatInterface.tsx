import React, { memo, useCallback, useEffect, useRef, useState } from 'react';
import { ANONYMOUS_USER_QUOTA, INITIAL_MESSAGE } from '../constants';
import { apiFetch } from '../lib/api';
import { supabase } from '../lib/supabaseClient';
import { ChatMessage, ConversationStage, ConversationState, MessageRole, User } from '../types';
// Removed streaming imports - no longer needed
import { cn } from '../lib/utils';
import DocumentViewerModal from './DocumentViewerModal';
import { BotIcon, DocumentIcon, SendIcon, UserIcon } from './Icons';
import MarkdownRenderer from './MarkdownRenderer';
import SidePanel from './SidePanel';

const SUGGESTED_PROMPTS = [
  "Draft a simple Non-Disclosure Agreement (NDA)",
  "Create a freelance contract for a web developer",
  "Write a basic rental lease agreement clause",
];

interface ChatInterfaceProps {
  setView?: (view: 'home' | 'auth' | 'dashboard') => void;
  user?: User | null;
  onSaveDocument?: (content: string, name: string, folderId: string | null, clientId: string | null, options?: { tags?: string[] }) => void;
  initialPrompt?: string | null;
  onInitialPromptHandled?: () => void;
  isDemo?: boolean;
  fromTemplate?: boolean;
}

const ChatInterface: React.FC<ChatInterfaceProps> = memo(({ setView, user, onSaveDocument, initialPrompt, onInitialPromptHandled, isDemo = false, fromTemplate = false }) => {
  const [messages, setMessages] = useState<ChatMessage[]>([INITIAL_MESSAGE]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [anonymousQuota, setAnonymousQuota] = useState(ANONYMOUS_USER_QUOTA);
  const [reviewModalContent, setReviewModalContent] = useState<string | null>(null);
  const [isSidePanelExpanded, setIsSidePanelExpanded] = useState(() => {
    // Get persistent state from localStorage
    const saved = localStorage.getItem('sidePanelExpanded');
    return saved !== null ? JSON.parse(saved) : true;
  });

  // Enhanced conversation state
  const [conversationState, setConversationState] = useState<ConversationState>({
    currentStage: ConversationStage.INITIAL,
    collectedData: {},
    isComplete: false
  });
  const [isTyping, setIsTyping] = useState(false);
  const [currentStatus, setCurrentStatus] = useState<string | null>(null);

  // Save sidebar state to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem('sidePanelExpanded', JSON.stringify(isSidePanelExpanded));
  }, [isSidePanelExpanded]);
  const abortRef = useRef<AbortController | null>(null);
  const loadingTimeoutRef = useRef<number | null>(null);



  const extractDocumentFromResponse = (raw: string): { cleaned: string; hadMarkers: boolean; preContractText?: string; hasStageMarker?: boolean } => {
    const start = '---CONTRACT START---';
    const end = '---CONTRACT END---';
    const stageMarker = '---STAGE 1 COMPLETE---';

    // Check for stage marker first
    if (raw.includes(stageMarker)) {
      const stage1Text = raw.substring(0, raw.indexOf(stageMarker)).trim();
      const remainingText = raw.substring(raw.indexOf(stageMarker) + stageMarker.length).trim();

      // Check if remaining text has contract markers
      if (remainingText.includes(start) && remainingText.includes(end)) {
        const contractContent = remainingText.substring(
          remainingText.indexOf(start) + start.length,
          remainingText.indexOf(end)
        ).trim();
        return {
          cleaned: contractContent,
          hadMarkers: true,
          preContractText: stage1Text || undefined,
          hasStageMarker: true
        };
      }

      return {
        cleaned: remainingText || raw.trim(),
        hadMarkers: false,
        preContractText: stage1Text || undefined,
        hasStageMarker: true
      };
    }

    // Original contract marker logic
    if (raw.includes(start) && raw.includes(end)) {
      const preContractText = raw.substring(0, raw.indexOf(start)).trim();
      const between = raw.substring(raw.indexOf(start) + start.length, raw.indexOf(end)).trim();
      return {
        cleaned: between,
        hadMarkers: true,
        preContractText: preContractText || undefined
      };
    }

    // If there's a markdown or prose preface with '---' divider at top, drop up to last divider in first 500 chars
    const first500 = raw.slice(0, 500);
    if ((first500.match(/---/g) || []).length >= 1) {
      const lastDash = first500.lastIndexOf('---');
      if (lastDash >= 0) { raw = raw.slice(lastDash + 3); }
    }
    // Strip common prefaces like "Okay, here's..."
    raw = raw.replace(/^\s*(okay|sure|here's|here is)[^\n]*\n+/i, '');
    // If there's an obvious HTML start, cut everything before it
    const m = raw.match(/<(h1|h2|article|section|p|html)[\s>]/i);
    if (m && typeof m.index === 'number') {
      raw = raw.slice(m.index).trim();
    }
    return { cleaned: raw.trim(), hadMarkers: false };
  };


  const messagesEndRef = useRef<HTMLDivElement>(null);

  const isAnonymous = !user;
  const isPaid = !!user && (user.planName === 'Premium' || user.planName === 'Enterprise');
  // For paid plans, treat as unlimited; anonymous users have session-limited quota
  const remaining = isAnonymous ? anonymousQuota : (isPaid ? Infinity : ((user?.quotaTotal || 0) - (user?.quotaUsed || 0)));
  const atQuotaLimit = isPaid ? false : (remaining <= 0);

  // Chat session handled server-side per request

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(scrollToBottom, [messages]);

  // Handler functions for interactive messages
  const handleSuggestedResponse = useCallback((response: string) => {
    // Add the suggested response as a user message and process it
    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      role: MessageRole.USER,
      content: response
    };

    setMessages(prev => [...prev, userMessage]);
    setInput('');

    // Process the response through the conversational flow
    if (conversationState.currentStage === ConversationStage.INITIAL) {
      handleConversationalFlow(response);
    } else {
      handleStandardResponse(response);
    }
  }, [conversationState.currentStage]);

  const handleConfirmation = useCallback((messageId: string) => {
    // Add user confirmation message
    const confirmationMessage: ChatMessage = {
      id: Date.now().toString(),
      role: MessageRole.USER,
      content: "Yes, please continue with these details."
    };

    setMessages(prev => [...prev, confirmationMessage]);

    // Update conversation state to generating stage
    setConversationState(prev => ({
      ...prev,
      currentStage: ConversationStage.GENERATING_CONTRACT
    }));

    // Send confirmation to AI
    handleStandardResponse("Yes, please continue with these details.");
  }, []);

  const handleModification = useCallback((messageId: string) => {
    // Add user modification request
    const modifyMessage: ChatMessage = {
      id: Date.now().toString(),
      role: MessageRole.USER,
      content: "I'd like to modify some of the details. Let me provide updated information."
    };

    setMessages(prev => [...prev, modifyMessage]);

    // Reset conversation state to gathering requirements
    setConversationState(prev => ({
      ...prev,
      currentStage: ConversationStage.GATHERING_REQUIREMENTS,
      collectedData: {} // Clear collected data for fresh start
    }));

    // Handle modification request
    handleStandardResponse("I'd like to modify some of the details. Let me provide updated information.");
  }, []);

  // Helper function to get next conversation stage
  const getNextStage = (currentStage: ConversationStage): ConversationStage => {
    const stageOrder = [
      ConversationStage.INITIAL,
      ConversationStage.ANALYZING_REQUEST,
      ConversationStage.GATHERING_REQUIREMENTS,
      ConversationStage.CONFIRMING_DETAILS,
      ConversationStage.PLANNING_STRUCTURE,
      ConversationStage.GENERATING_OUTLINE,
      ConversationStage.GENERATING_CONTRACT,
      ConversationStage.REVIEW_READY,
      ConversationStage.COMPLETED
    ];

    const currentIndex = stageOrder.indexOf(currentStage);
    return currentIndex < stageOrder.length - 1 ? stageOrder[currentIndex + 1] : currentStage;
  };

  // Helper function to detect contract requests
  const isContractRequest = (prompt: string): boolean => {
    const contractKeywords = [
      'contract', 'agreement', 'nda', 'non-disclosure', 'lease', 'employment',
      'freelance', 'consulting', 'terms of service', 'privacy policy', 'draft',
      'create', 'generate', 'write'
    ];

    const lowerPrompt = prompt.toLowerCase();
    return contractKeywords.some(keyword => lowerPrompt.includes(keyword));
  };

  // Helper function to get stage progress percentage
  const getStageProgress = (stage: ConversationStage): number => {
    const stageProgressMap = {
      [ConversationStage.INITIAL]: 0,
      [ConversationStage.ANALYZING_REQUEST]: 15,
      [ConversationStage.GATHERING_REQUIREMENTS]: 30,
      [ConversationStage.CONFIRMING_DETAILS]: 60,
      [ConversationStage.PLANNING_STRUCTURE]: 75,
      [ConversationStage.GENERATING_OUTLINE]: 85,
      [ConversationStage.GENERATING_CONTRACT]: 95,
      [ConversationStage.REVIEW_READY]: 100,
      [ConversationStage.COMPLETED]: 100
    };

    return stageProgressMap[stage] || 0;
  };

  // Helper function to get stage description
  const getStageDescription = (stage: ConversationStage): string => {
    const stageDescriptions = {
      [ConversationStage.INITIAL]: 'Getting started',
      [ConversationStage.ANALYZING_REQUEST]: 'Understanding your request',
      [ConversationStage.GATHERING_REQUIREMENTS]: 'Collecting requirements',
      [ConversationStage.CONFIRMING_DETAILS]: 'Confirming details',
      [ConversationStage.PLANNING_STRUCTURE]: 'Planning document structure',
      [ConversationStage.GENERATING_OUTLINE]: 'Creating outline',
      [ConversationStage.GENERATING_CONTRACT]: 'Generating your document',
      [ConversationStage.REVIEW_READY]: 'Ready for review',
      [ConversationStage.COMPLETED]: 'Complete'
    };

    return stageDescriptions[stage] || 'In progress';
  };

  // Enhanced conversational flow handler
  const handleConversationalFlow = async (prompt: string) => {
    setIsTyping(false);

    // Update conversation state
    setConversationState(prev => ({
      ...prev,
      currentStage: ConversationStage.ANALYZING_REQUEST
    }));

    // Add status message
    const statusMessage: ChatMessage = {
      id: `status-${Date.now()}`,
      role: MessageRole.MODEL,
      content: "Analyzing your request...",
      messageType: 'status',
      statusType: 'thinking'
    };

    setMessages(prev => [...prev, statusMessage]);

    // Simulate analysis time with status updates
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Update status message
    setMessages(prev => prev.map(msg =>
      msg.messageType === 'status'
        ? { ...msg, content: "Understanding your requirements...", statusType: 'thinking' as const }
        : msg
    ));

    await new Promise(resolve => setTimeout(resolve, 2000));

    setMessages(prev => prev.map(msg =>
      msg.messageType === 'status'
        ? { ...msg, content: "Preparing clarifying questions...", statusType: 'researching' as const }
        : msg
    ));

    // Use the conversational API to generate the response
    try {
      const json = await apiFetch<{
        text: string;
        nextStage?: string;
        extractedData?: Record<string, any>;
        messageType?: string;
        suggestedResponses?: string[];
        requiresConfirmation?: boolean;
      }>(`/api/ai/chat/conversational`, {
        method: 'POST',
        body: JSON.stringify({
          prompt,
          conversationHistory: [],
          stage: 'initial',
          collectedData: {}
        })
      });

      const response = json.text || '';

      // Create enhanced message with interactive properties
      const questionMessage: ChatMessage = {
        id: `question-${Date.now()}`,
        role: MessageRole.MODEL,
        content: response,
        messageType: (json.messageType as any) || 'question',
        suggestedResponses: json.suggestedResponses || [
          "Let me provide the details",
          "I need help with the requirements",
          "Use standard terms for now"
        ]
      };

      // Remove status message and add question
      setMessages(prev => prev.filter(msg => msg.messageType !== 'status').concat(questionMessage));

      // Update conversation state
      setConversationState(prev => ({
        ...prev,
        currentStage: (json.nextStage as ConversationStage) || ConversationStage.GATHERING_REQUIREMENTS,
        collectedData: { ...prev.collectedData, ...json.extractedData }
      }));

    } catch (error) {
      console.error('Conversational API failed, using fallback:', error);
      // Fallback to hardcoded questions
      const questionMessage: ChatMessage = {
        id: `question-${Date.now()}`,
        role: MessageRole.MODEL,
        content: `I'd be happy to help you create that document! To ensure I draft exactly what you need, I have a few questions:

**1. Who are the parties involved?**
- Individual names or company names

**2. What jurisdiction should this apply to?**
- State/country where the agreement will be enforced

**3. Are there any specific terms or requirements?**
- Special clauses, payment terms, duration, etc.`,
        messageType: 'question',
        suggestedResponses: [
          "Let me provide the details",
          "I need help with the requirements",
          "Use standard terms for now"
        ]
      };

      setMessages(prev => prev.filter(msg => msg.messageType !== 'status').concat(questionMessage));

      setConversationState(prev => ({
        ...prev,
        currentStage: ConversationStage.GATHERING_REQUIREMENTS
      }));
    }

    setIsLoading(false);
  };

  // Standard response handler (existing logic)
  const handleStandardResponse = async (prompt: string) => {
    setIsTyping(false);

    const modelPlaceholder: ChatMessage = { id: `${Date.now()}-streaming`, role: MessageRole.MODEL, content: '' };
    setMessages(prev => [...prev, modelPlaceholder]);

    // Use the new conversational API endpoint
    try {
      const conversationHistory = messages
        .filter(msg => msg.role === MessageRole.USER || msg.role === MessageRole.MODEL)
        .map(msg => ({ role: msg.role, content: msg.content }));

      const json = await apiFetch<{
        text: string;
        nextStage?: string;
        extractedData?: Record<string, any>;
        messageType?: string;
        suggestedResponses?: string[];
        requiresConfirmation?: boolean;
      }>(`/api/ai/chat/conversational`, {
        method: 'POST',
        body: JSON.stringify({
          prompt,
          conversationHistory,
          stage: conversationState.currentStage,
          collectedData: conversationState.collectedData
        })
      });

      const response = json.text || '';

      // Update conversation state if provided
      if (json.nextStage) {
        setConversationState(prev => ({
          ...prev,
          currentStage: json.nextStage as ConversationStage,
          collectedData: { ...prev.collectedData, ...json.extractedData }
        }));
      }

      // Create enhanced message with interactive properties
      const enhancedMessage: ChatMessage = {
        id: `${Date.now()}-response`,
        role: MessageRole.MODEL,
        content: response,
        messageType: json.messageType as any,
        suggestedResponses: json.suggestedResponses,
        requiresConfirmation: json.requiresConfirmation,
        conversationData: json.extractedData
      };

      // Replace placeholder with enhanced message
      setMessages(prev => {
        const newMessages = [...prev];
        const lastMsgIndex = newMessages.length - 1;
        if (lastMsgIndex >= 0 && newMessages[lastMsgIndex].role === MessageRole.MODEL) {
          newMessages[lastMsgIndex] = enhancedMessage;
        }
        return newMessages;
      });

    } catch (error) {
      console.error('Conversational API failed, falling back to standard API:', error);

      try {
        // Fallback to standard API
        const json = await apiFetch<{ text: string }>(`/api/ai/chat`, {
          method: 'POST',
          body: JSON.stringify({ prompt })
        });

        const response = json.text || '';

        setMessages(prev => {
          const newMessages = [...prev];
          const lastMsg = newMessages[newMessages.length - 1];
          if (lastMsg && lastMsg.role === MessageRole.MODEL) {
            lastMsg.content = response;
          }
          return newMessages;
        });
      } catch (fallbackError) {
        console.error('Both APIs failed:', fallbackError);

        // Show error message to user
        setMessages(prev => {
          const newMessages = [...prev];
          const lastMsg = newMessages[newMessages.length - 1];
          if (lastMsg && lastMsg.role === MessageRole.MODEL) {
            lastMsg.content = 'I apologize, but I encountered an error while processing your request. Please try again.';
          }
          return newMessages;
        });

        setError('Failed to generate response. Please try again.');
      }
    }

    setIsLoading(false);
  };

  // Helper function to render different message types
  const renderMessage = (msg: ChatMessage, index: number) => {
    // Handle typing indicator
    if (msg.isTyping) {
      return (
        <div key={msg.id} className="flex items-start gap-4">
          <div className="flex-shrink-0 w-10 h-10 rounded-full bg-brand-100 dark:bg-brand-900/50 flex items-center justify-center border border-brand-200 dark:border-brand-800">
            <BotIcon className="w-6 h-6 text-brand-700 dark:text-brand-400" />
          </div>
          <div className="px-5 py-3 rounded-2xl bg-zinc-100 dark:bg-zinc-800 text-zinc-800 dark:text-zinc-200">
            <div className="flex items-center gap-3">
              <div className="flex space-x-1">
                <div className="w-2 h-2 bg-zinc-400 dark:bg-zinc-500 rounded-full animate-bounce [animation-delay:-0.3s]"></div>
                <div className="w-2 h-2 bg-zinc-400 dark:bg-zinc-500 rounded-full animate-bounce [animation-delay:-0.15s]"></div>
                <div className="w-2 h-2 bg-zinc-400 dark:bg-zinc-500 rounded-full animate-bounce"></div>
              </div>
              <span className="text-sm text-zinc-500 dark:text-zinc-400">Lexi is thinking...</span>
            </div>
          </div>
        </div>
      );
    }

    // Handle status messages
    if (msg.messageType === 'status') {
      return (
        <div key={msg.id} className="flex items-start gap-4">
          <div className="flex-shrink-0 w-10 h-10 rounded-full bg-brand-100 dark:bg-brand-900/50 flex items-center justify-center border border-brand-200 dark:border-brand-800">
            <BotIcon className="w-6 h-6 text-brand-700 dark:text-brand-400" />
          </div>
          <div className="px-5 py-3 rounded-2xl bg-zinc-100 dark:bg-zinc-800 text-zinc-800 dark:text-zinc-200">
            <div className="flex items-center gap-3">
              <div className="flex space-x-1">
                <div className="w-2 h-2 bg-brand-500 rounded-full animate-bounce [animation-delay:-0.3s]"></div>
                <div className="w-2 h-2 bg-brand-500 rounded-full animate-bounce [animation-delay:-0.15s]"></div>
                <div className="w-2 h-2 bg-brand-500 rounded-full animate-bounce"></div>
              </div>
              <span className="text-sm text-zinc-600 dark:text-zinc-400">{msg.content}</span>
            </div>
          </div>
        </div>
      );
    }

    // Handle interactive messages (questions, confirmations)
    if (msg.messageType === 'question' || msg.messageType === 'confirmation') {
      return (
        <div key={msg.id} className="flex items-start gap-4 animate-fade-in">
          <div className="flex-shrink-0 w-10 h-10 rounded-full bg-brand-100 dark:bg-brand-900/50 flex items-center justify-center border border-brand-200 dark:border-brand-800">
            <BotIcon className="w-6 h-6 text-brand-700 dark:text-brand-400" />
          </div>
          <div className="flex-1 min-w-0">
            <div className="px-5 py-3 rounded-2xl bg-zinc-100 dark:bg-zinc-800 text-zinc-800 dark:text-zinc-200 transform transition-all duration-300 hover:scale-[1.01]">
              <MarkdownRenderer content={msg.content} />
            </div>

            {/* Show collected data summary if available */}
            {msg.conversationData && Object.keys(msg.conversationData).length > 0 && (
              <div className="mt-3 p-3 bg-brand-50 dark:bg-brand-900/20 rounded-lg border border-brand-200 dark:border-brand-800">
                <h4 className="text-sm font-medium text-brand-700 dark:text-brand-300 mb-2">
                  Collected Information:
                </h4>
                <div className="space-y-1 text-sm text-brand-600 dark:text-brand-400">
                  {Object.entries(msg.conversationData).map(([key, value]) => (
                    <div key={key} className="flex justify-between">
                      <span className="capitalize">{key.replace(/([A-Z])/g, ' $1').trim()}:</span>
                      <span className="font-medium">{Array.isArray(value) ? value.join(', ') : String(value)}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Suggested responses */}
            {msg.suggestedResponses && msg.suggestedResponses.length > 0 && (
              <div className="mt-3 flex flex-wrap gap-2 animate-slide-up">
                {msg.suggestedResponses.map((response, idx) => (
                  <button
                    key={idx}
                    onClick={() => handleSuggestedResponse(response)}
                    className="px-3 py-2 text-sm bg-white dark:bg-zinc-700 border border-zinc-300 dark:border-zinc-600 rounded-lg hover:bg-zinc-50 dark:hover:bg-zinc-600 transition-all duration-200 hover:scale-105 hover:shadow-md"
                    style={{ animationDelay: `${idx * 100}ms` }}
                  >
                    {response}
                  </button>
                ))}
              </div>
            )}

            {/* Confirmation buttons */}
            {msg.requiresConfirmation && (
              <div className="mt-3 flex gap-2 animate-slide-up">
                <button
                  onClick={() => handleConfirmation(msg.id)}
                  className="px-4 py-2 bg-brand-600 text-white rounded-lg hover:bg-brand-700 transition-all duration-200 hover:scale-105 hover:shadow-lg flex items-center gap-2"
                >
                  <span>✓</span>
                  Continue
                </button>
                <button
                  onClick={() => handleModification(msg.id)}
                  className="px-4 py-2 bg-zinc-200 dark:bg-zinc-700 text-zinc-700 dark:text-zinc-300 rounded-lg hover:bg-zinc-300 dark:hover:bg-zinc-600 transition-all duration-200 hover:scale-105 flex items-center gap-2"
                >
                  <span>✏️</span>
                  Modify
                </button>
              </div>
            )}
          </div>
        </div>
      );
    }

    // Default message rendering (existing logic)
    return (
      <div key={msg.id} className={`flex items-start gap-4 ${msg.role === MessageRole.USER ? 'justify-end' : ''}`}>
        {msg.role === MessageRole.MODEL && (
          <div className="flex-shrink-0 w-10 h-10 rounded-full bg-brand-100 dark:bg-brand-900/50 flex items-center justify-center border border-brand-200 dark:border-brand-800">
            <BotIcon className="w-6 h-6 text-brand-700 dark:text-brand-400" />
          </div>
        )}
        <div className={`w-full max-w-xl ${msg.role === MessageRole.USER ? 'order-2' : ''}`}>
          {msg.isContract ? (
            <div className="bg-zinc-50 dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg p-4">
              <div className="flex items-center text-zinc-700 dark:text-zinc-200 font-semibold mb-3">
                <DocumentIcon className="w-5 h-5 mr-2" />
                <span>Generated Legal Document</span>
              </div>
              <div className="max-h-48 overflow-y-auto p-2 bg-white dark:bg-zinc-900 rounded border border-zinc-200 dark:border-zinc-700 text-sm text-zinc-800 dark:text-zinc-300">
                <MarkdownRenderer content={msg.content.split('\n').slice(0, 10).join('\n') + "\n..."} />
              </div>
              <div className="mt-3">
                <button
                  onClick={() => setReviewModalContent(msg.content)}
                  className="w-full text-center px-4 py-2 text-sm font-semibold text-white bg-brand-600 hover:bg-brand-700 rounded-lg shadow-sm"
                >
                  Review & Save
                </button>
              </div>
            </div>
          ) : (
            <div className={`px-5 py-3 rounded-2xl ${msg.role === MessageRole.USER ? 'bg-brand-600 text-white' : 'bg-zinc-100 dark:bg-zinc-800 text-zinc-800 dark:text-zinc-200'}`}>
              <MarkdownRenderer content={msg.content} />
              {isLoading && index === messages.length - 1 && <span className="inline-block w-2 h-4 bg-zinc-600 dark:bg-zinc-400 animate-pulse ml-1" />}
            </div>
          )}
        </div>
        {msg.role === MessageRole.USER && (
          <div className="flex-shrink-0 w-10 h-10 rounded-full bg-zinc-200 dark:bg-zinc-700 flex items-center justify-center order-1">
            <UserIcon className="w-6 h-6 text-zinc-600 dark:text-zinc-300" />
          </div>
        )}
      </div>
    );
  };

  const handleSendMessage = useCallback(async (prompt: string) => {
    if (!prompt.trim() || isLoading) {
      return;
    }

    if (atQuotaLimit) {
      setError(isAnonymous ? "You have reached your free generation limit for this session." : "You have reached your monthly generation limit.");
      return;
    }

    const userMessage: ChatMessage = { id: Date.now().toString(), role: MessageRole.USER, content: prompt };

    // Add user message immediately
    setMessages(prev => prev.length === 1 && prev[0].id === 'initial-message' ? [userMessage] : [...prev, userMessage]);
    setInput('');
    setIsLoading(true);
    setError(null);

    // Show typing indicator
    setIsTyping(true);

    // Add a small delay to show typing indicator
    await new Promise(resolve => setTimeout(resolve, 1000));

    if (loadingTimeoutRef.current) { window.clearTimeout(loadingTimeoutRef.current); }
    loadingTimeoutRef.current = window.setTimeout(() => {
      setIsLoading(false);
      setIsTyping(false);
    }, 60000);

    // Determine if this should be a conversational response
    const shouldUseConversationalFlow = isContractRequest(prompt) && conversationState.currentStage === ConversationStage.INITIAL;

    if (shouldUseConversationalFlow) {
      await handleConversationalFlow(prompt);
    } else {
      await handleStandardResponse(prompt);
    }

    let fullResponse = '';
    try {
      const { data } = await supabase.auth.getSession();
      const _token = data.session?.access_token;
      const effectivePrompt = prompt;

      const json = await apiFetch<{ text: string }>(`/api/ai/chat`, { method: 'POST', body: JSON.stringify({ prompt: effectivePrompt }) });
      fullResponse = (json.text || '').trim();
    } catch {
      setMessages(prev => {
        const newMessages = [...prev];
        const lastMsg = newMessages[newMessages.length - 1];
        if (lastMsg && lastMsg.role === MessageRole.MODEL) {
          lastMsg.content = 'Error generating response.';
        }
        return newMessages;
      });
      if (loadingTimeoutRef.current) { window.clearTimeout(loadingTimeoutRef.current); }
      setIsLoading(false);
      return;
    } finally {
      abortRef.current = null;
      if (loadingTimeoutRef.current) { window.clearTimeout(loadingTimeoutRef.current); }
    }

    // Only process response if we have content
    if (!fullResponse.trim()) {
      setIsLoading(false);
      return;
    }

    let isContract = false;
    let contractText = '';

    // Use extractDocumentFromResponse to handle stage markers properly
    const { cleaned, hadMarkers, preContractText, hasStageMarker } = extractDocumentFromResponse(fullResponse);

    // Remove the placeholder message and add new messages
    setMessages(prev => {
      const newMessages = [...prev];
      // Remove the last placeholder message
      newMessages.pop();

      if (hasStageMarker) {
        // Stage marker detected
        // Add Stage 1 message if it has content
        if (preContractText && preContractText.trim()) {
          // Adding Stage 1 message
          newMessages.push({
            id: `msg-${Date.now()}-stage1`,
            role: MessageRole.MODEL,
            content: preContractText,
            isContract: false
          });
        }

        // Add the main content (Stage 2)
        if (cleaned && cleaned.trim()) {
          isContract = hadMarkers;
          contractText = hadMarkers ? cleaned : '';
          newMessages.push({
            id: `msg-${Date.now()}-stage2-main`,
            role: MessageRole.MODEL,
            content: cleaned,
            isContract: hadMarkers
          });
        }
      } else {
        // No stage marker - process as single response

        // Add pre-contract text as separate message if it exists
        if (preContractText && preContractText.trim()) {
          newMessages.push({
            id: `msg-${Date.now()}-pre`,
            role: MessageRole.MODEL,
            content: preContractText,
            isContract: false
          });
        }

        // Add the main content
        if (cleaned && cleaned.trim()) {
          if (hadMarkers) {
            isContract = true;
            contractText = cleaned;
          }
          newMessages.push({
            id: `msg-${Date.now()}-main`,
            role: MessageRole.MODEL,
            content: cleaned,
            isContract: hadMarkers
          });
        } else if (fullResponse.trim()) {
          // Fallback to original response if extraction failed
          newMessages.push({
            id: `msg-${Date.now()}`,
            role: MessageRole.MODEL,
            content: fullResponse,
            isContract: false
          });
        }
      }

      return newMessages;
    });

    // Auto-open modal logic: if markers present (isContract)
    if (isContract) {
      setReviewModalContent(contractText);
    }

    // Clear loading state
    setIsLoading(false);

  }, [isLoading, atQuotaLimit, isAnonymous]);

  const initialPromptHandledRef = useRef(false);

  // Reset the ref when initialPrompt changes
  useEffect(() => {
    initialPromptHandledRef.current = false;
  }, [initialPrompt]);

  useEffect(() => {
    if (initialPrompt && onInitialPromptHandled && !initialPromptHandledRef.current) {
      initialPromptHandledRef.current = true;
      handleSendMessage(initialPrompt);
      onInitialPromptHandled();
    }
  }, [initialPrompt, onInitialPromptHandled]); // Removed handleSendMessage from dependencies


  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleSendMessage(input);
  }

  const handleSaveFromModal = (documentName: string, folderId: string | null, htmlContent: string, clientId: string | null) => {
    if (isAnonymous) {
      setAnonymousQuota(prev => prev - 1);
      // Anonymous user saved document, quota decremented
    } else if (onSaveDocument) {
      const tags: string[] = [];
      // If the flow originated from a template selection, tag as template-origin
      if (fromTemplate) { tags.push('origin:template'); }
      onSaveDocument(htmlContent, documentName, folderId, clientId, { tags });
    }

    setReviewModalContent(null);
  };

  // Helper to fetch the latest assistant message and determine if it's reviewable
  const lastModelMessage: ChatMessage | undefined = (() => {
    for (let i = messages.length - 1; i >= 0; i--) {
      if (messages[i].role === MessageRole.MODEL) { return messages[i]; }
    }
    return undefined;
  })();
  const lastText = lastModelMessage?.content || '';
  const lastLooksHtml = /<h\d|<p|<ul|<ol|<table|<section/i.test(lastText);
  const lastIsLarge = lastText.replace(/\s+/g, ' ').length > 1200;
  const canReviewNow = !!lastModelMessage && (lastModelMessage.isContract || lastLooksHtml || lastIsLarge);

  if (isDemo) {
    const demoMessages: ChatMessage[] = [
      { id: 'demo1', role: MessageRole.USER, content: "Draft a simple Non-Disclosure Agreement for me." },
      { id: 'demo2', role: MessageRole.MODEL, content: "Of course, I can help with that. Generating the NDA now..." },
      { id: 'demo3', role: MessageRole.MODEL, content: `<h1>Mutual Non-Disclosure Agreement</h1><p>This Agreement is made between Party A and Party B...</p>`, isContract: true },
    ];
    return (
      <div className="flex flex-col h-[500px] bg-white dark:bg-zinc-900 border border-zinc-200 dark:border-zinc-800 rounded-2xl shadow-2xl shadow-zinc-200/50 dark:shadow-black/20">
        <div className="flex-1 overflow-y-auto p-6 space-y-6 pointer-events-none">
          {demoMessages.map((msg) => (
            <div key={msg.id} className={cn('flex items-start gap-4', msg.role === MessageRole.USER ? 'justify-end' : '')}>
              {msg.role === MessageRole.MODEL && (
                <div className="flex-shrink-0 w-10 h-10 rounded-full bg-brand-100 dark:bg-brand-900/50 flex items-center justify-center border border-brand-200 dark:border-brand-800">
                  <BotIcon className="w-6 h-6 text-brand-700 dark:text-brand-400" />
                </div>
              )}
              <div className={cn('w-full max-w-xl', msg.role === MessageRole.USER ? 'order-2' : '')}>
                {msg.isContract ? (
                  <div className="bg-zinc-50 dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 rounded-lg p-4">
                    <div className="flex items-center text-zinc-700 dark:text-zinc-200 font-semibold mb-3">
                      <DocumentIcon className="w-5 h-5 mr-2" />
                      <span>Generated Legal Document</span>
                    </div>
                    <div className="max-h-48 overflow-y-auto p-2 bg-white dark:bg-zinc-900 rounded border border-zinc-200 dark:border-zinc-700 text-sm text-zinc-800 dark:text-zinc-300">
                      <MarkdownRenderer content={msg.content.split('\n').slice(0, 10).join('\n') + "\n..."} />
                    </div>
                    <div className="mt-3">
                      <button className="w-full text-center px-4 py-2 text-sm font-semibold text-white bg-brand-600 rounded-lg shadow-sm">
                        Review & Save
                      </button>
                    </div>
                  </div>
                ) : (
                  <div className={cn('px-5 py-3 rounded-2xl', msg.role === MessageRole.USER ? 'bg-brand-600 text-white' : 'bg-zinc-100 dark:bg-zinc-800 text-zinc-800 dark:text-zinc-200')}>
                    <MarkdownRenderer content={msg.content} />
                  </div>
                )}
              </div>
              {msg.role === MessageRole.USER && (
                <div className="flex-shrink-0 w-10 h-10 rounded-full bg-zinc-200 dark:bg-zinc-700 flex items-center justify-center order-1">
                  <UserIcon className="w-6 h-6 text-zinc-600 dark:text-zinc-300" />
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    );
  }


  // Create quota text JSX for the side panel
  const quotaText = atQuotaLimit ? (
    isAnonymous ? (
      <span>
        Quota limit reached. {setView && <button onClick={() => setView('auth')} className="font-medium text-brand-600 hover:text-brand-700">Sign up</button>} for more generations.
      </span>
    ) : (
      <span>Monthly quota reached. Upgrade to Premium for unlimited generations.</span>
    )
  ) : (
    <span>
      {isPaid ? 'Unlimited generations available.' : `You have ${remaining} generation${remaining !== 1 ? 's' : ''} remaining.`}
    </span>
  );

  return (
    <div className="relative flex h-[70vh] max-h-[700px] bg-white dark:bg-zinc-900 border border-zinc-200 dark:border-zinc-800 rounded-2xl shadow-2xl shadow-zinc-200/50 dark:shadow-black/20">
      <SidePanel
        suggestedPrompts={SUGGESTED_PROMPTS}
        onPromptClick={handleSendMessage}
        quotaText={quotaText}
        isLoading={isLoading}
        atQuotaLimit={atQuotaLimit}
        isExpanded={isSidePanelExpanded}
        onToggle={() => setIsSidePanelExpanded(!isSidePanelExpanded)}
      />
      <div className={cn(
        "flex flex-col flex-1 min-w-0 transition-all duration-300",
        isSidePanelExpanded ? "ml-72" : "ml-0"
      )}>
        <div className="flex-1 overflow-y-auto p-6 space-y-6">
          {/* Progress indicator for conversation stages */}
          {conversationState.currentStage !== ConversationStage.INITIAL && (
            <div className="mb-4 p-4 bg-brand-50 dark:bg-brand-900/20 rounded-lg border border-brand-200 dark:border-brand-800">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-brand-700 dark:text-brand-300">
                  Conversation Progress
                </span>
                <span className="text-xs text-brand-600 dark:text-brand-400">
                  {getStageProgress(conversationState.currentStage)}% Complete
                </span>
              </div>
              <div className="w-full bg-brand-200 dark:bg-brand-800 rounded-full h-2">
                <div
                  className="bg-brand-600 h-2 rounded-full transition-all duration-500 ease-out"
                  style={{ width: `${getStageProgress(conversationState.currentStage)}%` }}
                ></div>
              </div>
              <div className="mt-2 text-xs text-brand-600 dark:text-brand-400">
                {getStageDescription(conversationState.currentStage)}
              </div>
            </div>
          )}

          {messages.map((msg, index) => renderMessage(msg, index))}
          {isTyping && (
            <div className="flex items-start gap-4 animate-fade-in">
              <div className="flex-shrink-0 w-10 h-10 rounded-full bg-brand-100 dark:bg-brand-900/50 flex items-center justify-center border border-brand-200 dark:border-brand-800">
                <BotIcon className="w-6 h-6 text-brand-700 dark:text-brand-400" />
              </div>
              <div className="px-5 py-3 rounded-2xl bg-zinc-100 dark:bg-zinc-800 text-zinc-800 dark:text-zinc-200">
                <div className="flex items-center gap-3">
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-zinc-400 dark:bg-zinc-500 rounded-full animate-bounce [animation-delay:-0.3s]"></div>
                    <div className="w-2 h-2 bg-zinc-400 dark:bg-zinc-500 rounded-full animate-bounce [animation-delay:-0.15s]"></div>
                    <div className="w-2 h-2 bg-zinc-400 dark:bg-zinc-500 rounded-full animate-bounce"></div>
                  </div>
                  <span className="text-sm text-zinc-500 dark:text-zinc-400">Lexi is thinking...</span>
                </div>
              </div>
            </div>
          )}
          <div ref={messagesEndRef} />
        </div>
        <div className="p-4 border-t border-zinc-200 dark:border-zinc-800 bg-white dark:bg-zinc-900 rounded-b-2xl">

          <form onSubmit={handleSubmit} className="flex items-center gap-2">
            <input
              type="text"
              value={input}
              onChange={(e) => setInput(e.target.value)}
              placeholder={atQuotaLimit ? (isAnonymous ? "Sign up to continue." : "Upgrade to continue.") : "Describe the contract you need..."}
              className="flex-1 w-full px-4 py-3 bg-zinc-100 dark:bg-zinc-800 rounded-xl focus:outline-none focus:ring-2 focus:ring-brand-500 transition disabled:opacity-50"
              disabled={isLoading || atQuotaLimit}
            />
            <button
              type="submit"
              className="flex-shrink-0 w-12 h-12 bg-brand-600 text-white rounded-xl flex items-center justify-center hover:bg-brand-700 disabled:bg-zinc-300 dark:disabled:bg-zinc-700 transition-colors shadow-sm"
              disabled={isLoading || !input.trim() || atQuotaLimit}
            >
              <SendIcon className="w-6 h-6" />
            </button>
            {isLoading && (
              <button
                type="button"
                onClick={() => { abortRef.current?.abort(); setIsLoading(false); }}
                className="flex-shrink-0 px-3 h-12 rounded-xl border border-zinc-300 dark:border-zinc-700 text-zinc-700 dark:text-zinc-200 hover:bg-zinc-100 dark:hover:bg-zinc-800"
              >
                Stop
              </button>
            )}

            {!isLoading && (
              <button
                type="button"
                onClick={() => canReviewNow && setReviewModalContent(lastText)}
                disabled={!canReviewNow}
                className={cn(
                  'flex-shrink-0 px-3 h-12 rounded-xl border transition-colors',
                  canReviewNow
                    ? 'border-brand-600 text-brand-700 hover:bg-brand-50 dark:text-brand-400 dark:hover:bg-brand-900/30'
                    : 'border-zinc-300 dark:border-zinc-700 text-zinc-400 cursor-not-allowed'
                )}
                title={canReviewNow ? 'Open Review & Save' : 'No document to review yet'}
              >
                Review & Save
              </button>
            )}
          </form>

          {error && <p className="text-red-500 text-sm text-center mt-2">{error}</p>}
        </div>
      </div>

      <DocumentViewerModal
        isOpen={!!reviewModalContent}
        onClose={() => setReviewModalContent(null)}
        contractContent={reviewModalContent || ''}
        onSave={handleSaveFromModal}
        user={user}
      />
    </div>
  );
});

export default ChatInterface;
